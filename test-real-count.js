const axios = require('axios');
require('dotenv').config();

console.log('🔍 Testing REAL Total Count Discovery...');
console.log('='.repeat(70));

// Configuration
const baseUrl = process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

// Authentication function
async function getAccessToken() {
    try {
        console.log('🔐 Getting access token...');

        const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`,
            `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
            {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        console.log('✅ Access token obtained successfully');
        return tokenResponse.data.access_token;
    } catch (error) {
        console.error('❌ Error getting access token:', error.response?.data || error.message);
        throw error;
    }
}

// Test real count discovery
async function discoverRealCount() {
    try {
        const token = await getAccessToken();
        
        console.log('\n📊 DISCOVERING REAL TOTAL COUNT...');
        console.log('='.repeat(50));
        
        let totalCount = 0;
        let currentStart = 0;
        const batchSize = 1000;
        let apiCalls = 0;
        
        console.log(`🔍 Using progressive pagination (batchSize: ${batchSize})`);
        
        while (currentStart < 50000) { // Safety limit
            apiCalls++;
            console.log(`\n📄 Batch ${apiCalls}: start=${currentStart.toLocaleString()}, limit=${batchSize}`);
            
            try {
                const batch = await axios.post(`${baseUrl}/encompass/v1/borrowerContactSelector`, {
                    start: currentStart,
                    limit: batchSize
                }, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (batch.data && Array.isArray(batch.data) && batch.data.length > 0) {
                    totalCount += batch.data.length;
                    console.log(`   ✅ Got ${batch.data.length} contacts (total: ${totalCount.toLocaleString()})`);
                    
                    // If we got fewer than the batch size, we've reached the end
                    if (batch.data.length < batchSize) {
                        console.log(`   🎯 REACHED END! (${batch.data.length} < ${batchSize})`);
                        break;
                    }
                    
                    currentStart += batchSize;
                } else {
                    console.log(`   ⚠️ Empty batch - no more data`);
                    break;
                }
                
                // Rate limiting
                await new Promise(resolve => setTimeout(resolve, 300));
                
            } catch (error) {
                console.error(`   ❌ Batch failed:`, error.response?.status || error.message);
                break;
            }
        }
        
        console.log('\n🎉 REAL COUNT DISCOVERY COMPLETE!');
        console.log('='.repeat(50));
        console.log(`📊 REAL Total Borrower Contacts: ${totalCount.toLocaleString()}`);
        console.log(`🌐 API Calls Made: ${apiCalls}`);
        console.log(`📈 Previous estimate was: 1,000 (WRONG!)`);
        console.log(`🎯 Actual count is: ${totalCount.toLocaleString()} (${((totalCount/1000)*100).toFixed(1)}% more!)`);
        
        return totalCount;
        
    } catch (error) {
        console.error('❌ Real count discovery failed:', error.message);
        throw error;
    }
}

// Run the test
discoverRealCount()
    .then(realCount => {
        console.log(`\n✅ SUCCESS: Discovered ${realCount.toLocaleString()} total borrower contacts!`);
        process.exit(0);
    })
    .catch(error => {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    });
