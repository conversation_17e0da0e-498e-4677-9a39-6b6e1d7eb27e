import axios, { AxiosResponse } from 'axios';
import { logger } from '../utils/logger';

export interface GHLContact {
  id?: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  email?: string;
  phone?: string;
  address1?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  dateOfBirth?: string;
  companyName?: string;
  website?: string;
  timezone?: string;
  tags?: string[];
  customFields?: Array<{
    key: string;
    field_value: string;
  }>;
  source?: string;
  locationId: string;
}

export interface GHLContactResponse {
  contact: GHLContact;
  success: boolean;
  message?: string;
}

export interface GHLSearchResponse {
  contact?: GHLContact;
  contacts?: GHLContact[];
  success: boolean;
}

class GoHighLevelService {
  private readonly baseUrl: string;
  private readonly apiKey: string;
  private readonly locationId: string;
  private readonly version = '2021-07-28';
  private readonly timeout = 30000;
  private readonly maxRetries = 3;

  constructor() {
    this.baseUrl = process.env.GOHIGHLEVEL_API_URL || 'https://services.leadconnectorhq.com';
    this.apiKey = process.env.GOHIGHLEVEL_API_KEY || '';
    this.locationId = process.env.GOHIGHLEVEL_LOCATION_ID || '';

    if (!this.apiKey || !this.locationId) {
      throw new Error('GoHighLevel API key and location ID are required');
    }
  }

  /**
   * Get standard headers for GHL API requests
   */
  private getHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'Version': this.version,
      'Accept': 'application/json'
    };
  }

  /**
   * Make API request with retry logic
   */
  private async makeRequest<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response: AxiosResponse<T> = await axios({
          method,
          url,
          data,
          headers: this.getHeaders(),
          timeout: this.timeout
        });

        return response.data;
      } catch (error: any) {
        const isLastAttempt = attempt === this.maxRetries;
        
        if (error.response) {
          logger.error(`GHL API Error (attempt ${attempt}/${this.maxRetries}):`, {
            status: error.response.status,
            data: error.response.data,
            url,
            method
          });

          // Don't retry on authentication errors
          if (error.response.status === 401 || error.response.status === 403) {
            throw new Error(`GHL Authentication failed: ${JSON.stringify(error.response.data)}`);
          }

          // Don't retry on validation errors
          if (error.response.status === 422 && isLastAttempt) {
            throw new Error(`GHL Validation error: ${JSON.stringify(error.response.data)}`);
          }
        } else {
          logger.error(`GHL Network Error (attempt ${attempt}/${this.maxRetries}):`, error.message);
        }

        if (isLastAttempt) {
          throw error;
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }

    throw new Error('Max retries exceeded');
  }

  /**
   * Search for existing contact by email or phone
   */
  async searchContact(email?: string, phone?: string): Promise<GHLContact | null> {
    try {
      if (!email && !phone) {
        return null;
      }

      const searchData: any = {
        locationId: this.locationId
      };

      if (email) searchData.email = email;
      if (phone) searchData.phone = phone;

      const response = await this.makeRequest<GHLSearchResponse>(
        'POST',
        '/contacts/search/duplicate',
        searchData
      );

      return response.contact || null;
    } catch (error: any) {
      logger.error('Error searching GHL contact:', error.message);
      return null;
    }
  }

  /**
   * Create a new contact
   */
  async createContact(contactData: Omit<GHLContact, 'id'>): Promise<GHLContact> {
    try {
      // Ensure locationId is set
      const data = {
        ...contactData,
        locationId: this.locationId
      };

      const response = await this.makeRequest<GHLContactResponse>(
        'POST',
        '/contacts/',
        data
      );

      logger.info(`Created GHL contact: ${data.firstName} ${data.lastName}`);
      return response.contact;
    } catch (error: any) {
      logger.error('Error creating GHL contact:', error.message);
      throw error;
    }
  }

  /**
   * Update an existing contact
   */
  async updateContact(contactId: string, contactData: Partial<GHLContact>): Promise<GHLContact> {
    try {
      // Ensure locationId is set
      const data = {
        ...contactData,
        locationId: this.locationId
      };

      const response = await this.makeRequest<GHLContactResponse>(
        'PUT',
        `/contacts/${contactId}`,
        data
      );

      logger.info(`Updated GHL contact: ${contactId}`);
      return response.contact;
    } catch (error: any) {
      logger.error('Error updating GHL contact:', error.message);
      throw error;
    }
  }

  /**
   * Get contact by ID
   */
  async getContact(contactId: string): Promise<GHLContact | null> {
    try {
      const response = await this.makeRequest<GHLContactResponse>(
        'GET',
        `/contacts/${contactId}`
      );

      return response.contact;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      logger.error('Error getting GHL contact:', error.message);
      throw error;
    }
  }

  /**
   * Delete a contact
   */
  async deleteContact(contactId: string): Promise<boolean> {
    try {
      await this.makeRequest(
        'DELETE',
        `/contacts/${contactId}`
      );

      logger.info(`Deleted GHL contact: ${contactId}`);
      return true;
    } catch (error: any) {
      logger.error('Error deleting GHL contact:', error.message);
      return false;
    }
  }

  /**
   * Create or update contact (upsert)
   */
  async upsertContact(contactData: Omit<GHLContact, 'id'>): Promise<{
    contact: GHLContact;
    action: 'created' | 'updated';
  }> {
    try {
      // Search for existing contact
      const existingContact = await this.searchContact(contactData.email, contactData.phone);

      if (existingContact) {
        // Update existing contact
        const updatedContact = await this.updateContact(existingContact.id!, contactData);
        return {
          contact: updatedContact,
          action: 'updated'
        };
      } else {
        // Create new contact
        const newContact = await this.createContact(contactData);
        return {
          contact: newContact,
          action: 'created'
        };
      }
    } catch (error: any) {
      logger.error('Error upserting GHL contact:', error.message);
      throw error;
    }
  }

  /**
   * Add tags to contact
   */
  async addTagsToContact(contactId: string, tags: string[]): Promise<boolean> {
    try {
      await this.makeRequest(
        'POST',
        `/contacts/${contactId}/tags`,
        { tags }
      );

      logger.info(`Added tags to contact ${contactId}:`, tags);
      return true;
    } catch (error: any) {
      logger.error('Error adding tags to GHL contact:', error.message);
      return false;
    }
  }

  /**
   * Remove tags from contact
   */
  async removeTagsFromContact(contactId: string, tags: string[]): Promise<boolean> {
    try {
      await this.makeRequest(
        'DELETE',
        `/contacts/${contactId}/tags`,
        { tags }
      );

      logger.info(`Removed tags from contact ${contactId}:`, tags);
      return true;
    } catch (error: any) {
      logger.error('Error removing tags from GHL contact:', error.message);
      return false;
    }
  }

  /**
   * Get all contacts with pagination
   */
  async getAllContacts(limit = 100, startAfter?: string): Promise<{
    contacts: GHLContact[];
    meta: {
      total: number;
      nextStartAfter?: string;
    };
  }> {
    try {
      const params = new URLSearchParams({
        locationId: this.locationId,
        limit: limit.toString()
      });

      if (startAfter) {
        params.append('startAfter', startAfter);
      }

      const response = await this.makeRequest<{
        contacts: GHLContact[];
        meta: any;
      }>(
        'GET',
        `/contacts/?${params.toString()}`
      );

      return {
        contacts: response.contacts || [],
        meta: {
          total: response.meta?.total || 0,
          nextStartAfter: response.meta?.nextStartAfter
        }
      };
    } catch (error: any) {
      logger.error('Error getting all GHL contacts:', error.message);
      throw error;
    }
  }

  /**
   * Validate contact data before sending to GHL
   */
  validateContactData(contactData: Partial<GHLContact>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Check required fields
    if (!contactData.email && !contactData.phone) {
      errors.push('Either email or phone is required');
    }

    // Validate email format
    if (contactData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contactData.email)) {
      errors.push('Invalid email format');
    }

    // Validate phone format (basic check)
    if (contactData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(contactData.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.push('Invalid phone format');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Transform Encompass contact to GHL format
   */
  transformEncompassToGHL(encompassContact: any, type: 'borrower' | 'business' = 'borrower'): GHLContact {
    if (type === 'borrower') {
      return {
        firstName: encompassContact.firstName || '',
        lastName: encompassContact.lastName || '',
        name: `${encompassContact.firstName || ''} ${encompassContact.lastName || ''}`.trim(),
        email: encompassContact.personalEmail || encompassContact.businessEmail || encompassContact.email || '',
        phone: encompassContact.homePhone || encompassContact.workPhone || encompassContact.mobilePhone || '',
        address1: encompassContact.currentMailingAddress || '',
        city: encompassContact.homeCity || '',
        state: encompassContact.homeState || '',
        postalCode: encompassContact.homeZip || '',
        country: 'US',
        dateOfBirth: encompassContact.birthdate || '',
        companyName: encompassContact.employerName || '',
        source: 'Encompass API',
        locationId: this.locationId,
        tags: ['Mortgage Lead', 'Encompass Import'],
        customFields: [
          { key: 'encompass_id', field_value: encompassContact.id || '' },
          { key: 'contact_type', field_value: 'borrower' },
          { key: 'job_title', field_value: encompassContact.jobTitle || '' },
          { key: 'referral', field_value: encompassContact.referral || '' }
        ].filter(field => field.field_value !== '')
      };
    } else {
      return {
        firstName: encompassContact.contactName || encompassContact.name || '',
        lastName: '',
        name: encompassContact.contactName || encompassContact.name || '',
        email: encompassContact.email || encompassContact.businessEmail || '',
        phone: encompassContact.phone || encompassContact.workPhone || '',
        companyName: encompassContact.name || '',
        source: 'Encompass API',
        locationId: this.locationId,
        tags: ['Business Partner', 'Encompass Import'],
        customFields: [
          { key: 'encompass_id', field_value: encompassContact.id || '' },
          { key: 'contact_type', field_value: 'business' },
          { key: 'category', field_value: encompassContact.category || '' },
          { key: 'business_name', field_value: encompassContact.name || '' }
        ].filter(field => field.field_value !== '')
      };
    }
  }
}

export const ghlService = new GoHighLevelService();
