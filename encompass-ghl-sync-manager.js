const axios = require('axios');
const fs = require('fs');
const path = require('path');
const cron = require('node-cron');
require('dotenv').config();

console.log('🔄 ENCOMPASS TO GOHIGHLEVEL SYNC MANAGER');
console.log('='.repeat(70));

// Configuration
const ENCOMPASS_CONFIG = {
    baseUrl: process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com',
    username: process.env.ENCOMPASS_USERNAME,
    password: process.env.ENCOMPASS_PASSWORD,
    clientId: process.env.ENCOMPASS_CLIENT_ID,
    clientSecret: process.env.ENCOMPASS_CLIENT_SECRET
};

const GHL_CONFIG = {
    apiKey: process.env.GOHIGHLEVEL_API_KEY,
    baseUrl: process.env.GOHIGHLEVEL_API_URL || 'https://services.leadconnectorhq.com',
    locationId: process.env.GOHIGHLEVEL_LOCATION_ID
};

const SYNC_CONFIG = {
    cronSchedule: '0 */6 * * *', // Every 6 hours
    batchSize: 100,
    delayBetweenRequests: 300,
    maxRetries: 3
};

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// Job state management
const JOB_STATE_PATH = path.join(__dirname, 'job-state.json');
const SYNC_LOG_PATH = path.join(__dirname, 'sync-log.json');

class SyncManager {
    constructor() {
        this.isJobRunning = false;
        this.currentJobId = null;
        this.apiCallCount = 0;
        this.startTime = null;
        this.jobState = this.loadJobState();
    }

    loadJobState() {
        try {
            if (fs.existsSync(JOB_STATE_PATH)) {
                return JSON.parse(fs.readFileSync(JOB_STATE_PATH, 'utf8'));
            }
        } catch (error) {
            console.log('⚠️ Could not load job state, starting fresh');
        }
        return {
            isRunning: false,
            lastJobId: null,
            lastStartTime: null,
            lastEndTime: null,
            totalContactsProcessed: 0,
            lastSyncResults: null
        };
    }

    saveJobState() {
        try {
            fs.writeFileSync(JOB_STATE_PATH, JSON.stringify(this.jobState, null, 2));
        } catch (error) {
            console.error('❌ Error saving job state:', error.message);
        }
    }

    async makeApiCall(url, options, description) {
        this.apiCallCount++;
        console.log(`🌐 API Call ${this.apiCallCount}: ${description}`);
        
        for (let attempt = 1; attempt <= SYNC_CONFIG.maxRetries; attempt++) {
            try {
                const response = await axios(url, options);
                await new Promise(resolve => setTimeout(resolve, SYNC_CONFIG.delayBetweenRequests));
                return response.data;
            } catch (error) {
                console.error(`❌ Attempt ${attempt}/${SYNC_CONFIG.maxRetries} failed: ${error.message}`);
                if (attempt === SYNC_CONFIG.maxRetries) {
                    throw error;
                }
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }

    async getAccessToken() {
        console.log('🔐 Getting Encompass access token...');
        
        const tokenResponse = await this.makeApiCall(
            `${ENCOMPASS_CONFIG.baseUrl}/oauth2/v1/token`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${ENCOMPASS_CONFIG.clientId}:${ENCOMPASS_CONFIG.clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data: `grant_type=password&username=${encodeURIComponent(ENCOMPASS_CONFIG.username)}&password=${encodeURIComponent(ENCOMPASS_CONFIG.password)}`
            },
            'Getting access token'
        );

        console.log('✅ Access token obtained');
        return tokenResponse.access_token;
    }

    async getAllBorrowerContacts(token) {
        console.log('📥 Fetching ALL borrower contacts...');
        
        const contacts = await this.makeApiCall(
            `${ENCOMPASS_CONFIG.baseUrl}/encompass/v1/borrowerContactSelector`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: { start: 0, limit: 1000 } // Gets all 1000 unique contacts
            },
            'Getting all borrower contacts'
        );

        console.log(`✅ Retrieved ${contacts.length} borrower contacts`);
        return contacts || [];
    }

    async getAllBusinessContacts(token) {
        console.log('📥 Fetching ALL business contacts...');
        
        const contacts = await this.makeApiCall(
            `${ENCOMPASS_CONFIG.baseUrl}/encompass/v1/businessContactSelector`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: { start: 0, limit: 1000 } // Gets all 725 unique contacts
            },
            'Getting all business contacts'
        );

        console.log(`✅ Retrieved ${contacts.length} business contacts`);
        return contacts || [];
    }

    async getContactDetails(contactId, token, type = 'borrower') {
        const endpoint = type === 'borrower' 
            ? `${ENCOMPASS_CONFIG.baseUrl}/encompass/v1/borrowerContacts/${contactId}`
            : `${ENCOMPASS_CONFIG.baseUrl}/encompass/v1/businessContacts/${contactId}`;

        return await this.makeApiCall(
            endpoint,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            `Getting ${type} contact details for ${contactId}`
        );
    }

    transformToGHLFormat(contact, type = 'borrower') {
        if (type === 'borrower') {
            return {
                firstName: contact.firstName || '',
                lastName: contact.lastName || '',
                name: `${contact.firstName || ''} ${contact.lastName || ''}`.trim(),
                email: contact.personalEmail || contact.businessEmail || contact.primaryEmail || '',
                phone: contact.homePhone || contact.workPhone || contact.mobilePhone || contact.primaryPhone || '',
                address1: contact.currentMailingAddress || '',
                city: contact.homeCity || '',
                state: contact.homeState || '',
                postalCode: contact.homeZip || '',
                dateOfBirth: contact.birthdate || '',
                companyName: contact.employerName || '',
                customFields: {
                    encompass_id: contact.id,
                    contact_type: 'borrower',
                    job_title: contact.jobTitle || '',
                    referral: contact.referral || '',
                    borrower_type: contact.borrowerType || ''
                }
            };
        } else {
            return {
                firstName: contact.contactName || contact.name || '',
                lastName: '',
                name: contact.contactName || contact.name || '',
                email: contact.email || contact.businessEmail || contact.personalEmail || '',
                phone: contact.phone || contact.workPhone || contact.mobilePhone || '',
                companyName: contact.name || '',
                customFields: {
                    encompass_id: contact.id,
                    contact_type: 'business',
                    category: contact.category || '',
                    business_name: contact.name || ''
                }
            };
        }
    }

    async syncContactToGHL(ghlContact) {
        try {
            // Skip if no email or phone
            if (!ghlContact.email && !ghlContact.phone) {
                return { status: 'skipped', reason: 'no_contact_info' };
            }

            // Check if contact exists
            const searchQuery = ghlContact.email || ghlContact.phone;
            const existingContact = await this.makeApiCall(
                `${GHL_CONFIG.baseUrl}/contacts/search/duplicate`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${GHL_CONFIG.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    data: {
                        locationId: GHL_CONFIG.locationId,
                        email: ghlContact.email,
                        phone: ghlContact.phone
                    }
                },
                `Checking for existing contact: ${ghlContact.name}`
            );

            if (existingContact && existingContact.contact) {
                // Update existing contact
                const updateResult = await this.makeApiCall(
                    `${GHL_CONFIG.baseUrl}/contacts/${existingContact.contact.id}`,
                    {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${GHL_CONFIG.apiKey}`,
                            'Content-Type': 'application/json'
                        },
                        data: { ...ghlContact, locationId: GHL_CONFIG.locationId }
                    },
                    `Updating existing contact: ${ghlContact.name}`
                );

                console.log(`✅ Updated: ${ghlContact.name}`);
                return { status: 'updated', contactId: existingContact.contact.id };
            } else {
                // Create new contact
                const createResult = await this.makeApiCall(
                    `${GHL_CONFIG.baseUrl}/contacts/`,
                    {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${GHL_CONFIG.apiKey}`,
                            'Content-Type': 'application/json'
                        },
                        data: { ...ghlContact, locationId: GHL_CONFIG.locationId }
                    },
                    `Creating new contact: ${ghlContact.name}`
                );

                console.log(`✅ Created: ${ghlContact.name}`);
                return { status: 'created', contactId: createResult.contact?.id };
            }
        } catch (error) {
            console.error(`❌ Error syncing ${ghlContact.name}:`, error.message);
            return { status: 'error', error: error.message };
        }
    }

    async runFullSync() {
        if (this.isJobRunning) {
            console.log('⚠️ Job already running, skipping...');
            return null;
        }

        this.isJobRunning = true;
        this.currentJobId = `sync_${Date.now()}`;
        this.startTime = Date.now();
        this.apiCallCount = 0;

        // Update job state
        this.jobState.isRunning = true;
        this.jobState.lastJobId = this.currentJobId;
        this.jobState.lastStartTime = new Date().toISOString();
        this.saveJobState();

        console.log(`\n🚀 STARTING FULL SYNC - Job ID: ${this.currentJobId}`);
        console.log('='.repeat(70));

        const syncResults = {
            created: 0,
            updated: 0,
            skipped: 0,
            errors: 0,
            totalProcessed: 0,
            borrowerContacts: 0,
            businessContacts: 0
        };

        try {
            // Get access token
            const token = await this.getAccessToken();

            // Get all contacts
            const borrowerContacts = await this.getAllBorrowerContacts(token);
            const businessContacts = await this.getAllBusinessContacts(token);

            console.log(`\n📊 SYNC PLAN:`);
            console.log(`👤 Borrower Contacts: ${borrowerContacts.length}`);
            console.log(`🏢 Business Contacts: ${businessContacts.length}`);
            console.log(`🎯 Total to Process: ${borrowerContacts.length + businessContacts.length}`);

            // Process borrower contacts sequentially
            console.log(`\n🔄 PROCESSING BORROWER CONTACTS...`);
            for (let i = 0; i < borrowerContacts.length; i++) {
                const contact = borrowerContacts[i];
                console.log(`\n📋 Processing borrower ${i + 1}/${borrowerContacts.length}: ${contact.firstName} ${contact.lastName}`);

                try {
                    // Get detailed contact info
                    const detailedContact = await this.getContactDetails(contact.id, token, 'borrower');
                    
                    // Transform to GHL format
                    const ghlContact = this.transformToGHLFormat(detailedContact, 'borrower');
                    
                    // Sync to GoHighLevel
                    const syncResult = await this.syncContactToGHL(ghlContact);
                    
                    // Update counters
                    syncResults[syncResult.status]++;
                    syncResults.totalProcessed++;
                    syncResults.borrowerContacts++;

                } catch (error) {
                    console.error(`❌ Error processing borrower contact ${contact.id}:`, error.message);
                    syncResults.errors++;
                    syncResults.totalProcessed++;
                }
            }

            // Process business contacts sequentially
            console.log(`\n🔄 PROCESSING BUSINESS CONTACTS...`);
            for (let i = 0; i < businessContacts.length; i++) {
                const contact = businessContacts[i];
                console.log(`\n🏢 Processing business ${i + 1}/${businessContacts.length}: ${contact.name || contact.contactName}`);

                try {
                    // Get detailed contact info
                    const detailedContact = await this.getContactDetails(contact.id, token, 'business');
                    
                    // Transform to GHL format
                    const ghlContact = this.transformToGHLFormat(detailedContact, 'business');
                    
                    // Sync to GoHighLevel
                    const syncResult = await this.syncContactToGHL(ghlContact);
                    
                    // Update counters
                    syncResults[syncResult.status]++;
                    syncResults.totalProcessed++;
                    syncResults.businessContacts++;

                } catch (error) {
                    console.error(`❌ Error processing business contact ${contact.id}:`, error.message);
                    syncResults.errors++;
                    syncResults.totalProcessed++;
                }
            }

            // Final results
            const elapsedTime = (Date.now() - this.startTime) / 1000;
            console.log(`\n🎉 SYNC COMPLETED - Job ID: ${this.currentJobId}`);
            console.log('='.repeat(70));
            console.log(`✅ Created: ${syncResults.created}`);
            console.log(`🔄 Updated: ${syncResults.updated}`);
            console.log(`⚠️ Skipped: ${syncResults.skipped}`);
            console.log(`❌ Errors: ${syncResults.errors}`);
            console.log(`📊 Total Processed: ${syncResults.totalProcessed}`);
            console.log(`👤 Borrower Contacts: ${syncResults.borrowerContacts}`);
            console.log(`🏢 Business Contacts: ${syncResults.businessContacts}`);
            console.log(`🌐 API Calls Made: ${this.apiCallCount}`);
            console.log(`⏱️ Total Time: ${elapsedTime.toFixed(1)}s`);

            // Update job state
            this.jobState.isRunning = false;
            this.jobState.lastEndTime = new Date().toISOString();
            this.jobState.totalContactsProcessed += syncResults.totalProcessed;
            this.jobState.lastSyncResults = syncResults;
            this.saveJobState();

            // Save sync log
            const syncLog = {
                jobId: this.currentJobId,
                startTime: new Date(this.startTime).toISOString(),
                endTime: new Date().toISOString(),
                elapsedTime: elapsedTime,
                results: syncResults,
                apiCallsMade: this.apiCallCount
            };

            try {
                fs.writeFileSync(SYNC_LOG_PATH, JSON.stringify(syncLog, null, 2));
                console.log(`💾 Sync log saved to: ${SYNC_LOG_PATH}`);
            } catch (error) {
                console.error('❌ Error saving sync log:', error.message);
            }

            return syncResults;

        } catch (error) {
            console.error(`❌ Sync failed - Job ID: ${this.currentJobId}:`, error.message);
            
            // Update job state on error
            this.jobState.isRunning = false;
            this.jobState.lastEndTime = new Date().toISOString();
            this.saveJobState();
            
            throw error;
        } finally {
            this.isJobRunning = false;
            this.currentJobId = null;
        }
    }

    startCronJob() {
        console.log('⏰ Starting cron job for 6-hour sync...');
        console.log(`📅 Schedule: ${SYNC_CONFIG.cronSchedule} (every 6 hours)`);

        cron.schedule(SYNC_CONFIG.cronSchedule, async () => {
            console.log('\n🔔 Cron job triggered...');
            
            // Check if a job is already running
            if (this.isJobRunning) {
                console.log('⚠️ Previous sync still running, skipping this scheduled run');
                return;
            }

            try {
                await this.runFullSync();
                console.log('✅ Scheduled sync completed successfully');
            } catch (error) {
                console.error('❌ Scheduled sync failed:', error.message);
            }
        });

        console.log('✅ Cron job started successfully');
        console.log('🔄 Process will keep running for scheduled syncs...');
        console.log('Press Ctrl+C to stop');
    }
}

// Main execution
if (require.main === module) {
    const syncManager = new SyncManager();
    const mode = process.argv[2] || 'sync';

    if (mode === 'sync') {
        // Run one-time sync
        console.log('🎯 Mode: One-time Full Sync');
        syncManager.runFullSync()
            .then(result => {
                console.log('\n✅ One-time sync completed!');
                process.exit(0);
            })
            .catch(error => {
                console.error('❌ One-time sync failed:', error.message);
                process.exit(1);
            });
    } else if (mode === 'cron') {
        // Start cron job
        console.log('🎯 Mode: Start Cron Job');
        syncManager.startCronJob();

        // Run initial sync
        syncManager.runFullSync()
            .then(result => {
                console.log('\n✅ Initial sync completed!');
            })
            .catch(error => {
                console.error('❌ Initial sync failed:', error.message);
            });
    } else {
        console.log('❌ Invalid mode. Use "sync" or "cron"');
        process.exit(1);
    }
}

module.exports = { SyncManager };
