const axios = require('axios');
require('dotenv').config();

console.log('🔍 Testing Encompass API Pagination and Total Count...');
console.log('='.repeat(70));

// Configuration
const baseUrl = process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

// Authentication function
async function getAccessToken() {
    try {
        console.log('🔐 Getting access token...');

        const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`,
            `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
            {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        console.log('✅ Access token obtained successfully');
        return tokenResponse.data.access_token;
    } catch (error) {
        console.error('❌ Error getting access token:', error.response?.data || error.message);
        throw error;
    }
}

// Test different pagination approaches
async function testPagination() {
    try {
        const token = await getAccessToken();
        
        console.log('\n📊 Testing Borrower Contact Pagination...');
        console.log('='.repeat(50));
        
        // Test 1: Small batch to understand structure
        console.log('\n🧪 Test 1: Small batch (start=0, limit=5)');
        const smallBatch = await axios.post(`${baseUrl}/encompass/v1/borrowerContactSelector`, {
            start: 0,
            limit: 5
        }, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log(`✅ Small batch result: ${smallBatch.data.length} contacts`);
        if (smallBatch.data.length > 0) {
            console.log(`📋 Sample contact: ${JSON.stringify(smallBatch.data[0], null, 2)}`);
        }
        
        // Test 2: Medium batch
        console.log('\n🧪 Test 2: Medium batch (start=0, limit=100)');
        const mediumBatch = await axios.post(`${baseUrl}/encompass/v1/borrowerContactSelector`, {
            start: 0,
            limit: 100
        }, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log(`✅ Medium batch result: ${mediumBatch.data.length} contacts`);
        
        // Test 3: Large batch to find total
        console.log('\n🧪 Test 3: Large batch (start=0, limit=10000)');
        const largeBatch = await axios.post(`${baseUrl}/encompass/v1/borrowerContactSelector`, {
            start: 0,
            limit: 10000
        }, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log(`✅ Large batch result: ${largeBatch.data.length} contacts`);
        
        // Test 4: Pagination from different start points
        console.log('\n🧪 Test 4: Pagination test (start=100, limit=50)');
        const paginatedBatch = await axios.post(`${baseUrl}/encompass/v1/borrowerContactSelector`, {
            start: 100,
            limit: 50
        }, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log(`✅ Paginated batch result: ${paginatedBatch.data.length} contacts`);
        
        // Test 5: Check if there's a way to get total count
        console.log('\n🧪 Test 5: Testing for total count endpoint...');
        try {
            const countResponse = await axios.get(`${baseUrl}/encompass/v1/borrowerContacts/count`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            console.log(`✅ Count endpoint result: ${JSON.stringify(countResponse.data)}`);
        } catch (error) {
            console.log(`❌ Count endpoint not available: ${error.response?.status}`);
        }
        
        // Summary
        console.log('\n📊 PAGINATION SUMMARY:');
        console.log('='.repeat(50));
        console.log(`🎯 Estimated total contacts: ${largeBatch.data.length}${largeBatch.data.length === 10000 ? '+' : ''}`);
        console.log(`📄 Pagination works: ${paginatedBatch.data.length > 0 ? 'YES' : 'NO'}`);
        console.log(`🔢 Recommended approach: Use start=0, limit=1000+ to get all contacts`);
        console.log(`💡 For full sync: Start with large limit to get total, then paginate if needed`);
        
        return {
            totalEstimate: largeBatch.data.length,
            paginationWorks: paginatedBatch.data.length > 0,
            sampleContact: smallBatch.data[0] || null
        };
        
    } catch (error) {
        console.error('❌ Pagination test failed:', error.response?.data || error.message);
        throw error;
    }
}

// Run the test
testPagination()
    .then(result => {
        console.log('\n🎉 Pagination test completed successfully!');
        console.log(`📊 Results: ${JSON.stringify(result, null, 2)}`);
    })
    .catch(error => {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    });
