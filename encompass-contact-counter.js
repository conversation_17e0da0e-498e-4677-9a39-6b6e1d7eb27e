const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🔍 ENCOMPASS CONTACT COUNTER WITH ID LOGGING');
console.log('='.repeat(70));

// Configuration
const baseUrl = process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

// Create data directory if it doesn't exist
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

class ContactCounter {
    constructor() {
        this.apiCallCount = 0;
        this.startTime = Date.now();
        this.allBorrowerContactIds = [];
        this.allBusinessContactIds = [];
    }

    async makeApiCall(url, options, description) {
        this.apiCallCount++;
        console.log(`🌐 API Call ${this.apiCallCount}: ${description}`);
        
        try {
            const response = await axios(url, options);
            await new Promise(resolve => setTimeout(resolve, 300)); // Rate limiting
            return response.data;
        } catch (error) {
            console.error(`❌ ${description} failed:`, error.response?.data || error.message);
            return null;
        }
    }

    async getAccessToken() {
        try {
            console.log('🔐 Getting access token...');

            const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`,
                `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
                {
                    headers: {
                        'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                }
            );

            console.log('✅ Access token obtained successfully');
            return tokenResponse.data.access_token;
        } catch (error) {
            console.error('❌ Error getting access token:', error.response?.data || error.message);
            throw error;
        }
    }

    async countBorrowerContactsWithIds(token) {
        console.log(`\n📊 COUNTING BORROWER CONTACTS WITH ID LOGGING...`);
        console.log(`🔍 Using progressive pagination to discover true total...`);

        let totalCount = 0;
        let currentStart = 0;
        const batchSize = 1000;
        let consecutiveEmptyBatches = 0;
        const maxEmptyBatches = 2;
        const maxBatches = 50; // Safety limit
        let batchCount = 0;

        while (consecutiveEmptyBatches < maxEmptyBatches && batchCount < maxBatches) {
            batchCount++;
            console.log(`   📄 Batch ${batchCount}: start=${currentStart.toLocaleString()}, limit=${batchSize}`);
            
            const batch = await this.makeApiCall(
                `${baseUrl}/encompass/v1/borrowerContactSelector`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { start: currentStart, limit: batchSize }
                },
                `Getting borrower contacts batch ${batchCount} starting at ${currentStart}`
            );

            if (batch && Array.isArray(batch) && batch.length > 0) {
                totalCount += batch.length;
                
                // Extract and store contact IDs
                const batchIds = batch.map(contact => ({
                    id: contact.id,
                    firstName: contact.firstName || '',
                    lastName: contact.lastName || '',
                    email: contact.personalEmail || contact.businessEmail || contact.primaryEmail || '',
                    phone: contact.homePhone || contact.workPhone || contact.mobilePhone || contact.primaryPhone || '',
                    batchNumber: batchCount,
                    startIndex: currentStart
                }));
                
                this.allBorrowerContactIds.push(...batchIds);
                
                console.log(`   ✅ Got ${batch.length} contacts (total so far: ${totalCount.toLocaleString()})`);
                
                // If we got fewer than the batch size, we've reached the end
                if (batch.length < batchSize) {
                    console.log(`   🎯 REACHED END! (got ${batch.length} < ${batchSize})`);
                    break;
                }
                
                consecutiveEmptyBatches = 0;
                currentStart += batchSize;
            } else {
                console.log(`   ⚠️ Empty batch ${batchCount} at start=${currentStart.toLocaleString()}`);
                consecutiveEmptyBatches++;
                
                if (consecutiveEmptyBatches >= maxEmptyBatches) {
                    console.log(`   🛑 Stopping after ${maxEmptyBatches} consecutive empty batches`);
                    break;
                }
                
                currentStart += batchSize;
            }
        }

        if (batchCount >= maxBatches) {
            console.log(`   🛑 Reached safety limit of ${maxBatches} batches (${totalCount.toLocaleString()}+ contacts)`);
        }

        console.log(`📊 REAL total borrower contacts discovered: ${totalCount.toLocaleString()}`);
        console.log(`📄 Batches processed: ${batchCount}`);
        return totalCount;
    }

    async countBusinessContactsWithIds(token) {
        console.log(`\n📊 COUNTING BUSINESS CONTACTS WITH ID LOGGING...`);
        
        let totalCount = 0;
        let currentStart = 0;
        const batchSize = 1000;
        let consecutiveEmptyBatches = 0;
        const maxEmptyBatches = 2;
        const maxBatches = 20; // Smaller limit for business contacts
        let batchCount = 0;

        while (consecutiveEmptyBatches < maxEmptyBatches && batchCount < maxBatches) {
            batchCount++;
            console.log(`   📄 Business Batch ${batchCount}: start=${currentStart.toLocaleString()}, limit=${batchSize}`);
            
            const batch = await this.makeApiCall(
                `${baseUrl}/encompass/v1/businessContactSelector`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { start: currentStart, limit: batchSize }
                },
                `Getting business contacts batch ${batchCount} starting at ${currentStart}`
            );

            if (batch && Array.isArray(batch) && batch.length > 0) {
                totalCount += batch.length;
                
                // Extract and store business contact IDs
                const batchIds = batch.map(contact => ({
                    id: contact.id,
                    name: contact.name || '',
                    contactName: contact.contactName || '',
                    category: contact.category || '',
                    phone: contact.phone || contact.workPhone || contact.mobilePhone || '',
                    email: contact.email || contact.businessEmail || contact.personalEmail || '',
                    batchNumber: batchCount,
                    startIndex: currentStart
                }));
                
                this.allBusinessContactIds.push(...batchIds);
                
                console.log(`   ✅ Got ${batch.length} business contacts (total so far: ${totalCount.toLocaleString()})`);
                
                if (batch.length < batchSize) {
                    console.log(`   🎯 REACHED END! (got ${batch.length} < ${batchSize})`);
                    break;
                }
                
                consecutiveEmptyBatches = 0;
                currentStart += batchSize;
            } else {
                console.log(`   ⚠️ Empty business batch ${batchCount} at start=${currentStart.toLocaleString()}`);
                consecutiveEmptyBatches++;
                
                if (consecutiveEmptyBatches >= maxEmptyBatches) {
                    console.log(`   🛑 Stopping after ${maxEmptyBatches} consecutive empty batches`);
                    break;
                }
                
                currentStart += batchSize;
            }
        }

        console.log(`📊 REAL total business contacts discovered: ${totalCount.toLocaleString()}`);
        return totalCount;
    }

    saveContactIdsToJson() {
        const timestamp = Date.now();
        
        // Save borrower contact IDs
        if (this.allBorrowerContactIds.length > 0) {
            const borrowerFilename = `borrower-contact-ids-${this.allBorrowerContactIds.length}-contacts-${timestamp}.json`;
            const borrowerFilepath = path.join(dataDir, borrowerFilename);
            
            const borrowerData = {
                metadata: {
                    totalContacts: this.allBorrowerContactIds.length,
                    discoveredAt: new Date().toISOString(),
                    elapsedTime: (Date.now() - this.startTime) / 1000,
                    apiCallsMade: this.apiCallCount,
                    source: 'Encompass API - Progressive Pagination Count Discovery',
                    endpoint: 'POST /encompass/v1/borrowerContactSelector'
                },
                contactIds: this.allBorrowerContactIds
            };

            try {
                fs.writeFileSync(borrowerFilepath, JSON.stringify(borrowerData, null, 2));
                console.log(`💾 Borrower contact IDs saved to: ${borrowerFilename}`);
            } catch (error) {
                console.error('❌ Error saving borrower contact IDs:', error.message);
            }
        }

        // Save business contact IDs
        if (this.allBusinessContactIds.length > 0) {
            const businessFilename = `business-contact-ids-${this.allBusinessContactIds.length}-contacts-${timestamp}.json`;
            const businessFilepath = path.join(dataDir, businessFilename);
            
            const businessData = {
                metadata: {
                    totalContacts: this.allBusinessContactIds.length,
                    discoveredAt: new Date().toISOString(),
                    elapsedTime: (Date.now() - this.startTime) / 1000,
                    apiCallsMade: this.apiCallCount,
                    source: 'Encompass API - Progressive Pagination Count Discovery',
                    endpoint: 'POST /encompass/v1/businessContactSelector'
                },
                contactIds: this.allBusinessContactIds
            };

            try {
                fs.writeFileSync(businessFilepath, JSON.stringify(businessData, null, 2));
                console.log(`💾 Business contact IDs saved to: ${businessFilename}`);
            } catch (error) {
                console.error('❌ Error saving business contact IDs:', error.message);
            }
        }
    }

    async run() {
        try {
            const token = await this.getAccessToken();
            
            // Count borrower contacts with ID logging
            const borrowerCount = await this.countBorrowerContactsWithIds(token);
            
            // Count business contacts with ID logging
            const businessCount = await this.countBusinessContactsWithIds(token);
            
            // Save all IDs to JSON files
            this.saveContactIdsToJson();
            
            // Final summary
            console.log('\n🎉 CONTACT COUNT DISCOVERY COMPLETE!');
            console.log('='.repeat(70));
            console.log(`👤 Borrower Contacts: ${borrowerCount.toLocaleString()}`);
            console.log(`🏢 Business Contacts: ${businessCount.toLocaleString()}`);
            console.log(`🎯 TOTAL CONTACTS: ${(borrowerCount + businessCount).toLocaleString()}`);
            console.log(`🌐 Total API Calls: ${this.apiCallCount}`);
            console.log(`⏱️ Total Time: ${((Date.now() - this.startTime) / 1000).toFixed(1)}s`);
            console.log(`💾 All contact IDs saved to JSON files in ./data/ directory`);
            
            return {
                borrowerCount,
                businessCount,
                totalCount: borrowerCount + businessCount,
                borrowerIds: this.allBorrowerContactIds,
                businessIds: this.allBusinessContactIds
            };
            
        } catch (error) {
            console.error('❌ Contact counting failed:', error.message);
            throw error;
        }
    }
}

// Run the counter if this file is executed directly
if (require.main === module) {
    const counter = new ContactCounter();
    counter.run()
        .then(result => {
            console.log(`\n✅ SUCCESS: Discovered ${result.totalCount.toLocaleString()} total contacts!`);
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ Failed:', error.message);
            process.exit(1);
        });
}

module.exports = { ContactCounter };
