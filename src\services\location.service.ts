import axios from 'axios';
import { Request } from 'fastify';
import { logger } from '../utils/logger';

export interface LocationData {
  ip: string;
  country: string;
  countryCode: string;
  region: string;
  regionName: string;
  city: string;
  zip: string;
  lat: number;
  lon: number;
  timezone: string;
  isp: string;
  org: string;
  as: string;
  query: string;
}

export interface GeolocationResult {
  success: boolean;
  location?: LocationData;
  error?: string;
}

class LocationService {
  private readonly ipApiUrl = 'http://ip-api.com/json';
  private readonly ipInfoUrl = 'https://ipinfo.io';
  private readonly maxRetries = 3;
  private readonly timeout = 5000;

  /**
   * Get client IP address from request
   */
  getClientIP(request: Request): string {
    const forwarded = request.headers['x-forwarded-for'] as string;
    const realIP = request.headers['x-real-ip'] as string;
    const cfConnectingIP = request.headers['cf-connecting-ip'] as string;
    
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    if (realIP) {
      return realIP;
    }
    
    if (cfConnectingIP) {
      return cfConnectingIP;
    }
    
    return request.socket.remoteAddress || '127.0.0.1';
  }

  /**
   * Get location data using IP-API service (free, no API key required)
   */
  async getLocationByIP(ip: string): Promise<GeolocationResult> {
    try {
      // Skip local IPs
      if (this.isLocalIP(ip)) {
        return {
          success: false,
          error: 'Cannot geolocate local IP address'
        };
      }

      const response = await axios.get(`${this.ipApiUrl}/${ip}`, {
        timeout: this.timeout,
        params: {
          fields: 'status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,isp,org,as,query'
        }
      });

      if (response.data.status === 'success') {
        return {
          success: true,
          location: {
            ip: response.data.query,
            country: response.data.country,
            countryCode: response.data.countryCode,
            region: response.data.region,
            regionName: response.data.regionName,
            city: response.data.city,
            zip: response.data.zip,
            lat: response.data.lat,
            lon: response.data.lon,
            timezone: response.data.timezone,
            isp: response.data.isp,
            org: response.data.org,
            as: response.data.as,
            query: response.data.query
          }
        };
      } else {
        return {
          success: false,
          error: response.data.message || 'Failed to get location data'
        };
      }
    } catch (error: any) {
      logger.error('Error getting location by IP:', error);
      return {
        success: false,
        error: error.message || 'Location service unavailable'
      };
    }
  }

  /**
   * Get location data with fallback to multiple services
   */
  async getLocationWithFallback(ip: string): Promise<GeolocationResult> {
    let lastError = '';

    // Try primary service (IP-API)
    const primaryResult = await this.getLocationByIP(ip);
    if (primaryResult.success) {
      return primaryResult;
    }
    lastError = primaryResult.error || 'Primary service failed';

    // Try fallback service (IPInfo)
    try {
      const response = await axios.get(`${this.ipInfoUrl}/${ip}/json`, {
        timeout: this.timeout
      });

      if (response.data && response.data.ip) {
        const [lat, lon] = (response.data.loc || '0,0').split(',').map(Number);
        
        return {
          success: true,
          location: {
            ip: response.data.ip,
            country: response.data.country || '',
            countryCode: response.data.country || '',
            region: response.data.region || '',
            regionName: response.data.region || '',
            city: response.data.city || '',
            zip: response.data.postal || '',
            lat: lat || 0,
            lon: lon || 0,
            timezone: response.data.timezone || '',
            isp: response.data.org || '',
            org: response.data.org || '',
            as: '',
            query: response.data.ip
          }
        };
      }
    } catch (error: any) {
      lastError = error.message || 'All location services failed';
    }

    return {
      success: false,
      error: lastError
    };
  }

  /**
   * Get location from request with caching
   */
  async getLocationFromRequest(request: Request): Promise<GeolocationResult> {
    const ip = this.getClientIP(request);
    
    // Check if location is already cached in request
    if ((request as any).locationData) {
      return {
        success: true,
        location: (request as any).locationData
      };
    }

    const result = await this.getLocationWithFallback(ip);
    
    // Cache successful result in request
    if (result.success && result.location) {
      (request as any).locationData = result.location;
    }

    return result;
  }

  /**
   * Check if IP is local/private
   */
  private isLocalIP(ip: string): boolean {
    const localPatterns = [
      /^127\./,
      /^192\.168\./,
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^::1$/,
      /^fc00:/,
      /^fe80:/,
      /^localhost$/i
    ];

    return localPatterns.some(pattern => pattern.test(ip));
  }

  /**
   * Get country-specific configuration
   */
  getCountryConfig(countryCode: string) {
    const countryConfigs: Record<string, any> = {
      'NG': {
        currency: 'NGN',
        phonePrefix: '+234',
        timezone: 'Africa/Lagos',
        supportedPayments: ['bank_transfer', 'ussd', 'card', 'wallet'],
        kycRequired: true,
        maxTransactionLimit: 5000000, // 5M NGN
        language: 'en'
      },
      'US': {
        currency: 'USD',
        phonePrefix: '+1',
        timezone: 'America/New_York',
        supportedPayments: ['card', 'ach', 'wire'],
        kycRequired: true,
        maxTransactionLimit: 50000, // 50K USD
        language: 'en'
      },
      'GB': {
        currency: 'GBP',
        phonePrefix: '+44',
        timezone: 'Europe/London',
        supportedPayments: ['card', 'bank_transfer', 'faster_payments'],
        kycRequired: true,
        maxTransactionLimit: 40000, // 40K GBP
        language: 'en'
      }
    };

    return countryConfigs[countryCode] || countryConfigs['NG']; // Default to Nigeria
  }

  /**
   * Validate if service is available in location
   */
  isServiceAvailable(countryCode: string): boolean {
    const supportedCountries = ['NG', 'US', 'GB', 'CA', 'AU', 'ZA', 'KE', 'GH'];
    return supportedCountries.includes(countryCode);
  }

  /**
   * Get timezone offset for location
   */
  getTimezoneOffset(timezone: string): number {
    try {
      const now = new Date();
      const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
      const targetTime = new Date(utc.toLocaleString('en-US', { timeZone: timezone }));
      return (targetTime.getTime() - utc.getTime()) / (1000 * 60 * 60);
    } catch (error) {
      return 0; // Default to UTC
    }
  }

  /**
   * Format location for display
   */
  formatLocation(location: LocationData): string {
    const parts = [location.city, location.regionName, location.country].filter(Boolean);
    return parts.join(', ');
  }

  /**
   * Get distance between two coordinates (in kilometers)
   */
  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}

export const locationService = new LocationService();
