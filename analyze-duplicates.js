const fs = require('fs');
const path = require('path');

console.log('🔍 ANALYZING CONTACT ID DUPLICATES');
console.log('='.repeat(70));

// Find the most recent borrower contact file
const dataDir = path.join(__dirname, 'data');
const files = fs.readdirSync(dataDir).filter(f => f.startsWith('borrower-contact-ids-'));
const latestFile = files.sort().pop();

if (!latestFile) {
    console.error('❌ No borrower contact files found in data directory');
    process.exit(1);
}

console.log(`📁 Analyzing file: ${latestFile}`);

// Load the data
const filePath = path.join(dataDir, latestFile);
const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));

console.log(`📊 Total records in file: ${data.contactIds.length.toLocaleString()}`);

// Analyze duplicates
const idCounts = {};
const uniqueContacts = new Map();
let duplicateCount = 0;

data.contactIds.forEach((contact, index) => {
    const id = contact.id;
    
    if (idCounts[id]) {
        idCounts[id]++;
        duplicateCount++;
    } else {
        idCounts[id] = 1;
        uniqueContacts.set(id, contact);
    }
});

const uniqueCount = uniqueContacts.size;
const totalRecords = data.contactIds.length;

console.log('\n📈 DUPLICATE ANALYSIS RESULTS:');
console.log('='.repeat(50));
console.log(`📊 Total records: ${totalRecords.toLocaleString()}`);
console.log(`✅ Unique contacts: ${uniqueCount.toLocaleString()}`);
console.log(`🔄 Duplicate records: ${duplicateCount.toLocaleString()}`);
console.log(`📉 Duplicate percentage: ${((duplicateCount / totalRecords) * 100).toFixed(1)}%`);

// Find most duplicated contacts
const sortedDuplicates = Object.entries(idCounts)
    .filter(([id, count]) => count > 1)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);

console.log('\n🔝 TOP 10 MOST DUPLICATED CONTACTS:');
console.log('='.repeat(50));
sortedDuplicates.forEach(([id, count], index) => {
    const contact = uniqueContacts.get(id);
    const name = `${contact.firstName} ${contact.lastName}`.trim() || 'No Name';
    console.log(`${index + 1}. ${id} - ${name} (${count} times)`);
});

// Analyze batch distribution
console.log('\n📄 BATCH ANALYSIS:');
console.log('='.repeat(50));
const batchAnalysis = {};
data.contactIds.forEach(contact => {
    const batch = contact.batchNumber;
    if (!batchAnalysis[batch]) {
        batchAnalysis[batch] = { total: 0, unique: new Set() };
    }
    batchAnalysis[batch].total++;
    batchAnalysis[batch].unique.add(contact.id);
});

// Show first 10 batches
Object.keys(batchAnalysis).slice(0, 10).forEach(batch => {
    const analysis = batchAnalysis[batch];
    const uniqueInBatch = analysis.unique.size;
    const totalInBatch = analysis.total;
    const duplicatePercent = ((totalInBatch - uniqueInBatch) / totalInBatch * 100).toFixed(1);
    
    console.log(`Batch ${batch}: ${totalInBatch} total, ${uniqueInBatch} unique (${duplicatePercent}% duplicates)`);
});

// Check if contacts repeat across batches
console.log('\n🔄 CROSS-BATCH DUPLICATE ANALYSIS:');
console.log('='.repeat(50));

const contactBatches = {};
data.contactIds.forEach(contact => {
    const id = contact.id;
    if (!contactBatches[id]) {
        contactBatches[id] = new Set();
    }
    contactBatches[id].add(contact.batchNumber);
});

const crossBatchDuplicates = Object.entries(contactBatches)
    .filter(([id, batches]) => batches.size > 1)
    .length;

console.log(`🔄 Contacts appearing in multiple batches: ${crossBatchDuplicates.toLocaleString()}`);
console.log(`📊 Contacts appearing in only one batch: ${(uniqueCount - crossBatchDuplicates).toLocaleString()}`);

// Save unique contacts to new file
const uniqueContactsArray = Array.from(uniqueContacts.values());
const timestamp = Date.now();
const uniqueFilename = `unique-borrower-contacts-${uniqueCount}-${timestamp}.json`;
const uniqueFilepath = path.join(dataDir, uniqueFilename);

const uniqueData = {
    metadata: {
        ...data.metadata,
        originalTotalRecords: totalRecords,
        uniqueContacts: uniqueCount,
        duplicatesRemoved: duplicateCount,
        duplicatePercentage: ((duplicateCount / totalRecords) * 100).toFixed(1),
        deduplicatedAt: new Date().toISOString(),
        source: 'Deduplicated from ' + latestFile
    },
    contactIds: uniqueContactsArray
};

try {
    fs.writeFileSync(uniqueFilepath, JSON.stringify(uniqueData, null, 2));
    console.log(`\n💾 Unique contacts saved to: ${uniqueFilename}`);
} catch (error) {
    console.error('❌ Error saving unique contacts:', error.message);
}

console.log('\n🎯 SUMMARY:');
console.log('='.repeat(50));
console.log(`🚨 The API is returning MASSIVE duplicates!`);
console.log(`📊 Real unique borrower contacts: ${uniqueCount.toLocaleString()}`);
console.log(`🔄 Duplicate inflation factor: ${(totalRecords / uniqueCount).toFixed(1)}x`);
console.log(`💡 Recommendation: Use unique contact list for processing`);

// Check if we need to continue fetching
if (crossBatchDuplicates > uniqueCount * 0.8) {
    console.log(`\n⚠️  WARNING: High cross-batch duplication suggests API is cycling through same contacts`);
    console.log(`💡 Real total may be close to ${uniqueCount.toLocaleString()} contacts`);
} else {
    console.log(`\n✅ Low cross-batch duplication suggests more unique contacts may exist`);
    console.log(`💡 Consider fetching more batches to find additional unique contacts`);
}
