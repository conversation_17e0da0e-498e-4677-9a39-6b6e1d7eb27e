{"metadata": {"totalRecords": 25, "fetchedAt": "2025-08-29T15:12:17.107Z", "elapsedTime": 20.155, "apiCallsMade": 27, "source": "Encompass API - Multiple Endpoints", "endpoints": ["POST /encompass/v1/borrowerContactSelector", "GET /encompass/v1/borrowerContacts/{contactId}", "POST /encompass/v1/loanFolders", "GET /encompass/v1/loans/{loanId}", "GET /encompass/v1/loans/{loanId}/associates", "GET /encompass/v1/businessContacts/{contactId}"]}, "comprehensiveData": [{"borrower": {"id": "a26af9f3-2d41-4ae1-9a41-366437ff8aaa", "name": "BARBARA HERRERA VEGA", "firstName": "BARBARA", "lastName": "HERRERA VEGA", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1967-09-08T00:00:00Z", "address": {"city": "Op<PERSON> Locka", "state": "FL", "street1": "2430 NW 140th St", "street2": "", "zip": "33054-4059", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "<PERSON> realtor", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:04.203Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "7daa9d4a-5411-4a47-8650-0bf2946a5a4d", "name": "ORLANDO GIL", "firstName": "ORLANDO", "lastName": "GIL", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "************", "primary": ""}, "dateOfBirth": "1986-12-28T00:00:00Z", "address": {"city": "MIAMI", "state": "FL", "street1": "14736 SW 55TH TERR", "street2": "", "zip": "33185", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "<PERSON> realtor", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:04.719Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "95671b80-e118-4aa8-aaf9-cd55db29dbff", "name": "ARIANA RODRIGUEZ", "firstName": "ARIANA", "lastName": "RODRIGUEZ", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1980-06-12T00:00:00Z", "address": {"city": "Miami", "state": "FL", "street1": "14525 SW 170 ST", "street2": "", "zip": "33177", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "<PERSON><PERSON> realtor", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:05.196Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "609bf4b4-c530-4d0f-bfa5-c5278fc5173d", "name": "MANUEL JUANES", "firstName": "MANUEL", "lastName": "JUANES", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1966-01-16T00:00:00Z", "address": {"city": "Miami", "state": "FL", "street1": "13237 SW 46 Ln", "street2": "", "zip": "33175", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "<PERSON> realtor", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:05.736Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "380a9e11-7207-410c-b5a7-d9277439cccf", "name": "TONY MEDEROS", "firstName": "TONY", "lastName": "MEDEROS", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1960-01-08T00:00:00Z", "address": {"city": "Hialeah", "state": "FL", "street1": "610 E 24th ST", "street2": "", "zip": "33013", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "alex bejerano", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:06.342Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "ac709e4c-2a38-4b93-bcf2-6c6df13245dd", "name": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1988-09-29T00:00:00Z", "address": {"city": "Miami", "state": "FL", "street1": "9611 SW 77th AVE UNIT 208A", "street2": "", "zip": "33156", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "ALEX", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:06.852Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "773d01eb-c5e8-43a2-a5a6-028bec114ce9", "name": "<PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1966-12-05T00:00:00Z", "address": {"city": "Miramar", "state": "FL", "street1": "1811 SW 97th TERR", "street2": "", "zip": "33025", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "<PERSON>", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:07.392Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "1142bbe1-b4f9-4c44-b757-eb6895748024", "name": "<PERSON><PERSON>", "firstName": "Ken<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1973-05-01T00:00:00Z", "address": {"city": "Miramar", "state": "FL", "street1": "1811 SW 97th TERR", "street2": "", "zip": "33025", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "<PERSON>", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:07.902Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "d0323134-23a6-4b44-902f-52429fd6dc33", "name": "<PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "************", "primary": ""}, "dateOfBirth": "1967-03-02T00:00:00Z", "address": {"city": "Miami", "state": "FL", "street1": "4641 SW 127 CT", "street2": "", "zip": "33175", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "ALEX BEJERANO", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:08.450Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "e0c19257-b1d0-407a-bb63-652d647881d4", "name": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON>bel", "lastName": "<PERSON>", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1975-09-28T00:00:00Z", "address": {"city": "Miami", "state": "FL", "street1": "4641 SW 127 CT", "street2": "", "zip": "33175", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "ALEX BEJERANO", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:08.999Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "dd3e3e8b-adb0-412b-90e6-482ce0f3c428", "name": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1963-03-13T00:00:00Z", "address": {"city": "Miami", "state": "FL", "street1": "4421 SW 135 AVE", "street2": "", "zip": "33175", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "ALEX BEJERANO", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:09.506Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "d3944e6f-027a-437c-9038-b20418c16c41", "name": "<PERSON><PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1963-08-17T00:00:00Z", "address": {"city": "Miami", "state": "FL", "street1": "4421 SW 135 AVE", "street2": "", "zip": "33175", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "abejerano", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "ALEX BEJERANO", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:10.005Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "6037e9c0-c387-4201-a244-ecd845ffe3b5", "name": "<PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phones": {"home": "", "work": "************", "mobile": "************", "primary": ""}, "dateOfBirth": "1986-06-28T00:00:00Z", "address": {"city": "MIAMI", "state": "FL", "street1": "6755 SOUTH WATERWAY DRIVE", "street2": "", "zip": "33155", "unitType": ""}, "businessAddress": {"city": "miami", "state": "FL", "street1": "1007 N America Way", "street2": "", "zip": "33132", "unitType": ""}, "employer": {"name": "International Longshoremans Association", "jobTitle": "Checker"}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "acruz", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:10.498Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "78ba3cd8-2320-406a-b5dd-2c406042beab", "name": "<PERSON> ?ng<PERSON>s <PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON> ?ngeles <PERSON>", "email": "<EMAIL>", "phones": {"home": "", "work": "", "mobile": "************", "primary": ""}, "dateOfBirth": "1978-03-01T00:00:00Z", "address": {"city": "Miami", "state": "FL", "street1": "880 nw 118 st", "street2": "", "zip": "33168", "unitType": ""}, "businessAddress": {"city": "Miami lakes", "state": "FL", "street1": "5151 NW 165 st", "street2": "", "zip": "33014", "unitType": ""}, "employer": {"name": "Harvest Valley Inc", "jobTitle": "Driver"}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "acruz", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": "<PERSON>", "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:10.990Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "ae1bb7f9-caaf-4d40-b14a-5b213577e9c4", "name": "<PERSON>", "firstName": "Natalie", "lastName": "LaC<PERSON>ce", "email": "<EMAIL>", "phones": {"home": "************", "work": "************ 6853", "mobile": "************", "primary": ""}, "dateOfBirth": "1971-09-26T00:00:00Z", "address": {"city": "Hialeah", "state": "FL", "street1": "1355 W 44th place #230", "street2": "", "zip": "33012", "unitType": ""}, "businessAddress": {"city": "<PERSON><PERSON>", "state": "FL", "street1": "9700 NW 112th Ave", "street2": "", "zip": "33178-6853", "unitType": ""}, "employer": {"name": "TracFone Wireless Inc.", "jobTitle": "eLearning Administrator / TracFone Wireless Inc."}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "acruz", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:11.489Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "8ec2e727-8993-4986-97c7-925bc8d04f58", "name": "<PERSON>", "firstName": "<PERSON>", "lastName": "Poviones", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "************", "primary": ""}, "dateOfBirth": "1969-08-16T00:00:00Z", "address": {"city": "Hialeah", "state": "FL", "street1": "1355 W 44th place #230", "street2": "", "zip": "33012", "unitType": ""}, "businessAddress": {"city": "Opa-locka", "state": "FL", "street1": "12700 NW 42nd Ave", "street2": "", "zip": "33054", "unitType": ""}, "employer": {"name": "Insurance Auto Aution", "jobTitle": "Loader / Auto Auction"}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "acruz", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:12.024Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "05df7d75-1ed9-4820-86af-14a6d2c5c066", "name": "jennifer garat", "firstName": "jennifer", "lastName": "garat", "email": "<EMAIL>", "phones": {"home": "", "work": "", "mobile": "************", "primary": ""}, "dateOfBirth": "1976-09-12T00:00:00Z", "address": {"city": "Hialeah", "state": "FL", "street1": "7010 nw 186 st Apt 115", "street2": "", "zip": "33015", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "acruz", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:12.521Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "2b649dab-3c2a-454a-ab04-22ff5e07b15e", "name": "<PERSON>", "firstName": "<PERSON>", "lastName": "Street", "email": "", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "0001-01-01T00:00:00Z", "address": {"city": "Miami", "state": "FL", "street1": "10415 SW 153 Ct", "street2": "", "zip": "33196", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "admin", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:13.270Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "0b5086ed-e7c4-49f3-82c9-f3240dc8176c", "name": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON> ", "lastName": "<PERSON><PERSON><PERSON>", "email": "", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "0001-01-01T00:00:00Z", "address": {"city": "Hialeah", "state": "FL", "street1": "6520 W 12  Ave", "street2": "", "zip": "33012", "unitType": ""}, "businessAddress": {"city": "", "state": "", "street1": "", "street2": "", "zip": "", "unitType": ""}, "employer": {"name": "", "jobTitle": ""}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "admin", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:13.833Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "e508602b-**************-b998e0c53817", "name": "LISANDRA CAMACHO", "firstName": "LISANDRA", "lastName": "CAMACHO", "email": "<EMAIL>", "phones": {"home": "", "work": "************", "mobile": "************", "primary": ""}, "dateOfBirth": "1981-11-29T00:00:00Z", "address": {"city": "MIAMI", "state": "FL", "street1": "16519 SW 67 TERR", "street2": "", "zip": "33193", "unitType": ""}, "businessAddress": {"city": "MIAMI", "state": "FL", "street1": "1375 NW 36TH ST", "street2": "", "zip": "33142", "unitType": ""}, "employer": {"name": "CHECKMARK INSURANCE", "jobTitle": "OFFICE MANAGER / INSURANCE AGENCY"}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "admin", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:14.351Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "ee8e2f2d-3c50-4c2e-b5aa-2a8460e4581b", "name": "matilde <PERSON>", "firstName": "matilde", "lastName": "Mendoza", "email": "<EMAIL>", "phones": {"home": "************", "work": "************", "mobile": "", "primary": ""}, "dateOfBirth": "1982-03-07T00:00:00Z", "address": {"city": "miami", "state": "FL", "street1": "515 nw 20 ave", "street2": "", "zip": "33125", "unitType": ""}, "businessAddress": {"city": "Miami", "state": "FL", "street1": "1475 nw 12 ave", "street2": "", "zip": "33136", "unitType": ""}, "employer": {"name": "university of miami", "jobTitle": "pt access / hospital"}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "admin", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:14.903Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "5206f987-350f-4a98-85c6-aab1f38c5c47", "name": "el<PERSON><PERSON> morel", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "morel", "email": "<EMAIL>", "phones": {"home": "", "work": "************", "mobile": "************", "primary": ""}, "dateOfBirth": "1979-07-03T00:00:00Z", "address": {"city": "miami", "state": "FL", "street1": "515 nw 20 ave", "street2": "", "zip": "33125", "unitType": ""}, "businessAddress": {"city": "Miami", "state": "FL", "street1": "1710  nw 7 street", "street2": "", "zip": "33125", "unitType": ""}, "employer": {"name": "stadium cutz", "jobTitle": "barber / barber shop"}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "admin", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:15.439Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "e073c619-e97a-4a44-abf3-cdd7cc851695", "name": "<PERSON>", "firstName": "<PERSON>", "lastName": "Arteaga", "email": "<EMAIL>", "phones": {"home": "", "work": "************ 2585", "mobile": "************", "primary": ""}, "dateOfBirth": "1980-08-15T00:00:00Z", "address": {"city": "Miami", "state": "FL", "street1": "3230 nw 14st", "street2": "", "zip": "33125", "unitType": ""}, "businessAddress": {"city": "Miami", "state": "FL", "street1": "10200 Sunset Drive", "street2": "", "zip": "33173", "unitType": ""}, "employer": {"name": "Psychcare", "jobTitle": "Network Specialist / Healthcare"}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "admin", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:15.950Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "16dd2ea3-c9af-4a35-821d-cc74ceb8d487", "name": "<PERSON>", "firstName": "<PERSON>", "lastName": "Lopes", "email": "<EMAIL>", "phones": {"home": "************", "work": "************ 242", "mobile": "************", "primary": ""}, "dateOfBirth": "1985-04-06T00:00:00Z", "address": {"city": "miami", "state": "FL", "street1": "1010 NW 40th ave", "street2": "", "zip": "33126", "unitType": ""}, "businessAddress": {"city": "doral", "state": "FL", "street1": "8235 NW 56 street", "street2": "", "zip": "33166", "unitType": ""}, "employer": {"name": "Aircraft Systems Division of Com-Jet Corp.", "jobTitle": "director of sales / faa repair station"}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "admin", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:16.500Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}, {"borrower": {"id": "24f86ccd-fa7d-48d5-9418-77220661384d", "name": "<PERSON>", "firstName": "<PERSON>", "lastName": "Re<PERSON><PERSON>", "email": "<EMAIL>", "phones": {"home": "", "work": "************", "mobile": "************", "primary": ""}, "dateOfBirth": "1972-01-17T00:00:00Z", "address": {"city": "MIAMI", "state": "FL", "street1": "7780 SW 90th St. Apt: K2", "street2": "", "zip": "33156", "unitType": ""}, "businessAddress": {"city": "Miami", "state": "FL", "street1": "3460 Royal Rd.", "street2": "", "zip": "33133", "unitType": ""}, "employer": {"name": "St. Hugh School", "jobTitle": "Teacher / Private School"}, "borrowerType": 1, "salutation": "", "faxNumber": "", "businessWebUrl": null, "ownerId": "admin", "accessLevel": 0}, "loan": {"id": null, "guid": null, "interestRate": null, "closingDate": null, "propertyAddress": null, "loanOriginator": null, "amount": null, "type": null}, "realtor": {"name": null, "phone": null, "email": null, "businessContactId": null, "businessName": null, "category": null, "address": null, "website": null}, "dataSources": {"borrowerContact": true, "loanData": false, "loanAssociates": false, "realtorBusinessContact": false}, "metadata": {"extractedAt": "2025-08-29T15:12:17.103Z", "source": "Encompass API - Enhanced", "version": "2.0", "hasRealtorData": false, "hasLoanData": false}}]}