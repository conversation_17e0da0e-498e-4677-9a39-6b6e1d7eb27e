const axios = require('axios');
require('dotenv').config();

console.log('🔍 ENCOMPASS API COUNT DISCOVERY TEST');
console.log('='.repeat(70));

async function getToken() {
    const baseUrl = process.env.ENCOMPASS_API_URL;
    const username = process.env.ENCOMPASS_USERNAME;
    const password = process.env.ENCOMPASS_PASSWORD;
    const clientId = process.env.ENCOMPASS_CLIENT_ID;
    const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

    const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`,
        `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
        {
            headers: {
                'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
    );
    return tokenResponse.data.access_token;
}

async function testCountDiscovery() {
    try {
        console.log('🔐 Getting access token...');
        const token = await getToken();
        console.log('✅ Token obtained');

        console.log('\n📊 TESTING DIFFERENT BATCH SIZES...');
        
        // Test different limits to understand API behavior
        const testLimits = [1, 10, 100, 500, 1000, 2000, 5000];
        
        for (const limit of testLimits) {
            console.log(`\n🧪 Testing limit=${limit}:`);
            
            try {
                const response = await axios.post(`${process.env.ENCOMPASS_API_URL}/encompass/v1/borrowerContactSelector`, {
                    start: 0,
                    limit: limit
                }, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log(`   ✅ Requested: ${limit}, Got: ${response.data.length}`);
                
                // If we get fewer than requested, we might have found the total
                if (response.data.length < limit) {
                    console.log(`   🎯 POSSIBLE TOTAL FOUND: ${response.data.length}`);
                }
                
            } catch (error) {
                console.log(`   ❌ Failed: ${error.response?.status || error.message}`);
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        console.log('\n🔍 TESTING PAGINATION TO FIND REAL TOTAL...');
        
        let totalFound = 0;
        let currentStart = 0;
        const batchSize = 1000;
        let batchCount = 0;
        
        while (batchCount < 10) { // Limit to 10 batches for testing
            batchCount++;
            console.log(`\n📄 Batch ${batchCount}: start=${currentStart}, limit=${batchSize}`);
            
            try {
                const response = await axios.post(`${process.env.ENCOMPASS_API_URL}/encompass/v1/borrowerContactSelector`, {
                    start: currentStart,
                    limit: batchSize
                }, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.data && response.data.length > 0) {
                    totalFound += response.data.length;
                    console.log(`   ✅ Got ${response.data.length} contacts (total: ${totalFound})`);
                    
                    // If we got fewer than batch size, we've reached the end
                    if (response.data.length < batchSize) {
                        console.log(`   🎯 REACHED END! Total contacts: ${totalFound}`);
                        break;
                    }
                    
                    currentStart += batchSize;
                } else {
                    console.log(`   ⚠️ Empty batch - end reached`);
                    break;
                }
                
            } catch (error) {
                console.log(`   ❌ Batch failed: ${error.response?.status || error.message}`);
                break;
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        console.log('\n🎉 COUNT DISCOVERY RESULTS:');
        console.log('='.repeat(50));
        console.log(`📊 Total Borrower Contacts Found: ${totalFound.toLocaleString()}`);
        console.log(`📄 Batches Processed: ${batchCount}`);
        console.log(`🔍 API Behavior: Returns full batches until end`);
        console.log(`💡 Recommendation: Use progressive pagination to get real total`);
        
        return totalFound;
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        throw error;
    }
}

testCountDiscovery()
    .then(total => {
        console.log(`\n✅ SUCCESS: Found ${total.toLocaleString()} total borrower contacts!`);
    })
    .catch(error => {
        console.error('❌ Failed:', error.message);
    });
