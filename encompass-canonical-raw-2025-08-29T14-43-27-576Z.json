{"metadata": {"fetchedAt": "2025-08-29T14:43:27.576Z", "totalPages": 1, "totalRecords": 0, "canonicalFieldsRequested": 59, "paginationComplete": true, "apiServer": "https://api.elliemae.com"}, "canonicalFields": ["Contact.Id", "Contact.FirstName", "Contact.LastName", "Contact.HomePhone", "Contact.WorkPhone", "Contact.MobilePhone", "Contact.PrimaryPhone", "Contact.FaxNumber", "Contact.PersonalEmail", "Contact.BusinessEmail", "Contact.PrimaryEmail", "Contact.Birthdate", "Contact.CurrentMailingAddress", "Contact.BizAddress", "Contact.EmployerName", "Contact.JobTitle", "Contact.BorrowerType", "Contact.Salutation", "Contact.BusinessWebUrl", "Contact.OwnerId", "Contact.AccessLevel", "Contact.Referral", "Loan.LoanNumber", "Loan.G<PERSON>", "Loan.Fields.4", "Loan.InterestRate", "Loan.NoteRate", "Loan.Fields.763", "Loan.ClosingDate", "Loan.EstimatedClosingDate", "Loan.Fields.11", "Loan.PropertyAddress", "Loan.SubjectPropertyAddress", "Loan.Fields.1109", "Loan.LoanAmount", "Loan.RequestedLoanAmount", "Loan.Fields.1172", "Loan.LoanType", "Loan.Loan<PERSON>", "LoanAssociate.Name", "LoanAssociate.FirstName", "LoanAssociate.LastName", "LoanAssociate.Email", "LoanAssociate.Phone", "LoanAssociate.Role", "LoanAssociate.RoleType", "LoanAssociate.Id", "Realtor.Id", "Realtor.Name", "Realtor.Phone", "Realtor.WorkPhone", "Realtor.MobilePhone", "Realtor.Email", "Realtor.BusinessEmail", "Realtor.PersonalEmail", "Realtor.BusinessName", "Realtor.Category", "Realtor.Address", "Realtor.BusinessWebUrl"], "fieldDefinitions": [{"filterOnly": false, "category": "Borrower Contact", "description": "Borrower ID", "canonicalName": "Contact.Id", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Borrower First Name", "canonicalName": "Contact.FirstName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Borrower Last Name", "canonicalName": "Contact.LastName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Home Phone", "canonicalName": "Contact.HomePhone", "dataType": "PHONE", "dataSource": "BorrowerContact", "maxLength": 17}, {"filterOnly": false, "category": "Borrower Contact", "description": "Work Phone", "canonicalName": "Contact.WorkPhone", "dataType": "PHONE", "dataSource": "BorrowerContact", "maxLength": 17}, {"filterOnly": false, "category": "Borrower Contact", "description": "Cell Phone", "canonicalName": "Contact.MobilePhone", "dataType": "PHONE", "dataSource": "BorrowerContact", "maxLength": 17}, {"filterOnly": false, "category": "Borrower Contact", "description": "Primary Phone", "canonicalName": "Contact.PrimaryPhone", "dataType": "PHONE", "dataSource": "BorrowerContact", "maxLength": 17}, {"filterOnly": false, "category": "Borrower Contact", "description": "Fax Number", "canonicalName": "Contact.FaxNumber", "dataType": "PHONE", "dataSource": "BorrowerContact", "maxLength": 17}, {"filterOnly": false, "category": "Borrower Contact", "description": "Personal Email", "canonicalName": "Contact.PersonalEmail", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business Email", "canonicalName": "Contact.BusinessEmail", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Primary Email", "canonicalName": "Contact.PrimaryEmail", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Birthday", "canonicalName": "Contact.Birthdate", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Borrower Contact", "description": "Current Mailing Address", "canonicalName": "Contact.CurrentMailingAddress", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business Address", "canonicalName": "Contact.BizAddress", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Employer Name", "canonicalName": "Contact.EmployerName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Job Title", "canonicalName": "Contact.JobTitle", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Borrower Type", "canonicalName": "Contact.BorrowerType", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Salutation", "canonicalName": "Contact.Salutation", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business Web URL", "canonicalName": "Contact.BusinessWebUrl", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Owner ID", "canonicalName": "Contact.OwnerId", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Access Level", "canonicalName": "Contact.AccessLevel", "dataType": "STRING", "dataSource": "BorrowerContact", "options": ["Public", "Private"]}, {"filterOnly": false, "category": "Borrower Contact", "description": "Referral", "canonicalName": "Contact.Referral", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Loan Number", "canonicalName": "Loan.LoanNumber", "dataType": "STRING", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Loan G<PERSON>", "canonicalName": "Loan.G<PERSON>", "dataType": "STRING", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Interest Rate (Field 4)", "canonicalName": "Loan.Fields.4", "dataType": "DECIMAL_4", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Interest Rate", "canonicalName": "Loan.InterestRate", "dataType": "DECIMAL_4", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Note Rate", "canonicalName": "Loan.NoteRate", "dataType": "DECIMAL_4", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Closing Date (Field 763)", "canonicalName": "Loan.Fields.763", "dataType": "DATE", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Closing Date", "canonicalName": "Loan.ClosingDate", "dataType": "DATE", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Estimated Closing Date", "canonicalName": "Loan.EstimatedClosingDate", "dataType": "DATE", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Property Address (Field 11)", "canonicalName": "Loan.Fields.11", "dataType": "STRING", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Property Address", "canonicalName": "Loan.PropertyAddress", "dataType": "STRING", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Subject Property Address", "canonicalName": "Loan.SubjectPropertyAddress", "dataType": "STRING", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "<PERSON><PERSON> (Field 1109)", "canonicalName": "Loan.Fields.1109", "dataType": "DECIMAL_4", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "<PERSON><PERSON>", "canonicalName": "Loan.LoanAmount", "dataType": "DECIMAL_4", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Requested <PERSON><PERSON> Amount", "canonicalName": "Loan.RequestedLoanAmount", "dataType": "DECIMAL_4", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Loan Type (Field 1172)", "canonicalName": "Loan.Fields.1172", "dataType": "STRING", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "Loan Type", "canonicalName": "Loan.LoanType", "dataType": "STRING", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan <PERSON>", "description": "<PERSON><PERSON>", "canonicalName": "Loan.Loan<PERSON>", "dataType": "STRING", "dataSource": "LoanData"}, {"filterOnly": false, "category": "Loan Associates", "description": "Loan Originator Name", "canonicalName": "LoanAssociate.Name", "dataType": "STRING", "dataSource": "LoanAssociates"}, {"filterOnly": false, "category": "Loan Associates", "description": "Loan Originator First Name", "canonicalName": "LoanAssociate.FirstName", "dataType": "STRING", "dataSource": "LoanAssociates"}, {"filterOnly": false, "category": "Loan Associates", "description": "Loan Originator Last Name", "canonicalName": "LoanAssociate.LastName", "dataType": "STRING", "dataSource": "LoanAssociates"}, {"filterOnly": false, "category": "Loan Associates", "description": "Loan Originator <PERSON><PERSON>", "canonicalName": "LoanAssociate.Email", "dataType": "STRING", "dataSource": "LoanAssociates"}, {"filterOnly": false, "category": "Loan Associates", "description": "Loan Originator Phone", "canonicalName": "LoanAssociate.Phone", "dataType": "PHONE", "dataSource": "LoanAssociates"}, {"filterOnly": false, "category": "Loan Associates", "description": "Loan Originator Role", "canonicalName": "LoanAssociate.Role", "dataType": "STRING", "dataSource": "LoanAssociates"}, {"filterOnly": false, "category": "Loan Associates", "description": "Loan Originator Role Type", "canonicalName": "LoanAssociate.RoleType", "dataType": "STRING", "dataSource": "LoanAssociates"}, {"filterOnly": false, "category": "Loan Associates", "description": "Loan Originator ID", "canonicalName": "LoanAssociate.Id", "dataType": "STRING", "dataSource": "LoanAssociates"}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor ID", "canonicalName": "Realtor.Id", "dataType": "STRING", "dataSource": "RealtorData"}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor Name", "canonicalName": "Realtor.Name", "dataType": "STRING", "dataSource": "RealtorData"}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor Phone", "canonicalName": "Realtor.Phone", "dataType": "PHONE", "dataSource": "RealtorData", "maxLength": 17}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor Work Phone", "canonicalName": "Realtor.WorkPhone", "dataType": "PHONE", "dataSource": "RealtorData", "maxLength": 17}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor Mobile Phone", "canonicalName": "Realtor.MobilePhone", "dataType": "PHONE", "dataSource": "RealtorData", "maxLength": 17}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor <PERSON><PERSON>", "canonicalName": "Realtor.Email", "dataType": "STRING", "dataSource": "RealtorData"}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor Business Email", "canonicalName": "Realtor.BusinessEmail", "dataType": "STRING", "dataSource": "RealtorData"}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor Personal Email", "canonicalName": "Realtor.PersonalEmail", "dataType": "STRING", "dataSource": "RealtorData"}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor Business Name", "canonicalName": "Realtor.BusinessName", "dataType": "STRING", "dataSource": "RealtorData"}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor Category", "canonicalName": "Realtor.Category", "dataType": "STRING", "dataSource": "RealtorData"}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor Address", "canonicalName": "Realtor.Address", "dataType": "STRING", "dataSource": "RealtorData"}, {"filterOnly": false, "category": "Realtor Data", "description": "Realtor Website", "canonicalName": "Realtor.BusinessWebUrl", "dataType": "STRING", "dataSource": "RealtorData"}], "pages": [{"pageNumber": 1, "description": "Canonical Fields Fetch - Page 1 (start=0, limit=10000)", "count": 0, "fetchedAt": "2025-08-29T14:43:26.562Z", "error": {"summary": "Conflict", "details": "Invalid canonical name/s for fields list - Contact.Id, Contact.PrimaryPhone, Contact.BusinessEmail, Contact.PrimaryEmail, Contact.CurrentMailingAddress, Contact.BizAddress, Contact.BorrowerType, Contact.BusinessWebUrl, Contact.OwnerId, <PERSON><PERSON>.<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>.Fields.4, <PERSON><PERSON>.InterestRate, <PERSON><PERSON>.NoteRate, Loan.Fields.763, <PERSON><PERSON>.ClosingDate, Loan.EstimatedClosingDate, Loan.Fields.11, <PERSON><PERSON>.PropertyAddress, Loan.SubjectPropertyAddress, Loan.Fields.1109, <PERSON>an.LoanAmount, Loan.RequestedLoanAmount, Loan.Fields.1172, Loan.LoanType, <PERSON><PERSON>.<PERSON>an<PERSON><PERSON>, LoanAssociate.Name, LoanAssociate.FirstName, LoanAssociate.LastName, LoanAssociate.Email, LoanAssociate.Phone, LoanAssociate.Role, LoanAssociate.RoleType, LoanAssociate.Id, Realtor.Id, Realtor.Name, Realtor.Phone, Realtor.WorkPhone, Realtor.MobilePhone, Realtor.Email, Realtor.BusinessEmail, Realtor.PersonalEmail, Realtor.BusinessName, Realtor.Category, Realtor.Address, Realtor.BusinessWebUrl", "errorCode": "EBS-2508"}, "data": []}]}