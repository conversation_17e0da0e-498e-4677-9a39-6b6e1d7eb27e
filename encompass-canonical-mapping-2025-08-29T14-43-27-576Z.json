{"fieldMapping": {"timestamp": "2025-08-29T14:43:27.578Z", "totalFieldsDefined": 59, "selectableFields": 59, "fieldsByCategory": {"Borrower Contact": 22, "Loan Data": 17, "Loan Associates": 8, "Realtor Data": 12}}, "fieldDefinitions": [{"canonicalName": "Contact.Id", "description": "Borrower ID", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.FirstName", "description": "Borrower First Name", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.LastName", "description": "Borrower Last Name", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.HomePhone", "description": "Home Phone", "category": "Borrower Contact", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.WorkPhone", "description": "Work Phone", "category": "Borrower Contact", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.MobilePhone", "description": "Cell Phone", "category": "Borrower Contact", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.PrimaryPhone", "description": "Primary Phone", "category": "Borrower Contact", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.FaxNumber", "description": "Fax Number", "category": "Borrower Contact", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.PersonalEmail", "description": "Personal Email", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BusinessEmail", "description": "Business Email", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.PrimaryEmail", "description": "Primary Email", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.Birthdate", "description": "Birthday", "category": "Borrower Contact", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.CurrentMailingAddress", "description": "Current Mailing Address", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BizAddress", "description": "Business Address", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.EmployerName", "description": "Employer Name", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.JobTitle", "description": "Job Title", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BorrowerType", "description": "Borrower Type", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.Salutation", "description": "Salutation", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BusinessWebUrl", "description": "Business Web URL", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.OwnerId", "description": "Owner ID", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.AccessLevel", "description": "Access Level", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": ["Public", "Private"]}, {"canonicalName": "Contact.Referral", "description": "Referral", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Loan.LoanNumber", "description": "Loan Number", "category": "Loan <PERSON>", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Loan.G<PERSON>", "description": "Loan G<PERSON>", "category": "Loan <PERSON>", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Loan.Fields.4", "description": "Interest Rate (Field 4)", "category": "Loan <PERSON>", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Loan.InterestRate", "description": "Interest Rate", "category": "Loan <PERSON>", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Loan.NoteRate", "description": "Note Rate", "category": "Loan <PERSON>", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Loan.Fields.763", "description": "Closing Date (Field 763)", "category": "Loan <PERSON>", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "Loan.ClosingDate", "description": "Closing Date", "category": "Loan <PERSON>", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "Loan.EstimatedClosingDate", "description": "Estimated Closing Date", "category": "Loan <PERSON>", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "Loan.Fields.11", "description": "Property Address (Field 11)", "category": "Loan <PERSON>", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Loan.PropertyAddress", "description": "Property Address", "category": "Loan <PERSON>", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Loan.SubjectPropertyAddress", "description": "Subject Property Address", "category": "Loan <PERSON>", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Loan.Fields.1109", "description": "<PERSON><PERSON> (Field 1109)", "category": "Loan <PERSON>", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Loan.LoanAmount", "description": "<PERSON><PERSON>", "category": "Loan <PERSON>", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Loan.RequestedLoanAmount", "description": "Requested <PERSON><PERSON> Amount", "category": "Loan <PERSON>", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Loan.Fields.1172", "description": "Loan Type (Field 1172)", "category": "Loan <PERSON>", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Loan.LoanType", "description": "Loan Type", "category": "Loan <PERSON>", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Loan.Loan<PERSON>", "description": "<PERSON><PERSON>", "category": "Loan <PERSON>", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LoanAssociate.Name", "description": "Loan Originator Name", "category": "Loan Associates", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LoanAssociate.FirstName", "description": "Loan Originator First Name", "category": "Loan Associates", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LoanAssociate.LastName", "description": "Loan Originator Last Name", "category": "Loan Associates", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LoanAssociate.Email", "description": "Loan Originator <PERSON><PERSON>", "category": "Loan Associates", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LoanAssociate.Phone", "description": "Loan Originator Phone", "category": "Loan Associates", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "LoanAssociate.Role", "description": "Loan Originator Role", "category": "Loan Associates", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LoanAssociate.RoleType", "description": "Loan Originator Role Type", "category": "Loan Associates", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LoanAssociate.Id", "description": "Loan Originator ID", "category": "Loan Associates", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.Id", "description": "Realtor ID", "category": "Realtor Data", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.Name", "description": "Realtor Name", "category": "Realtor Data", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.Phone", "description": "Realtor Phone", "category": "Realtor Data", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.WorkPhone", "description": "Realtor Work Phone", "category": "Realtor Data", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.MobilePhone", "description": "Realtor Mobile Phone", "category": "Realtor Data", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.Email", "description": "Realtor <PERSON><PERSON>", "category": "Realtor Data", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.BusinessEmail", "description": "Realtor Business Email", "category": "Realtor Data", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.PersonalEmail", "description": "Realtor Personal Email", "category": "Realtor Data", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.BusinessName", "description": "Realtor Business Name", "category": "Realtor Data", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.Category", "description": "Realtor Category", "category": "Realtor Data", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.Address", "description": "Realtor Address", "category": "Realtor Data", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Realtor.BusinessWebUrl", "description": "Realtor Website", "category": "Realtor Data", "dataType": "STRING", "filterOnly": false, "options": null}]}